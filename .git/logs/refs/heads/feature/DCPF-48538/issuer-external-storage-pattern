0000000000000000000000000000000000000000 c5d8c82294589e5370818c2f1490ff35f772c598 volehong-sc <<EMAIL>> 1755500555 +0700	branch: Created from feature/DCPF-49358^0
c5d8c82294589e5370818c2f1490ff35f772c598 deb00e9463b1c9b41c9868962a9a2225d6a8d183 volehong-sc <<EMAIL>> 1755500774 +0700	commit: DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
deb00e9463b1c9b41c9868962a9a2225d6a8d183 da37a544aa2ec32f9db27b42182124ee50c01fdf volehong-sc <<EMAIL>> 1755500872 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
da37a544aa2ec32f9db27b42182124ee50c01fdf 3489088629cca288a68cbd60353d522c9b180f0b volehong-sc <<EMAIL>> 1755501660 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
3489088629cca288a68cbd60353d522c9b180f0b 4e057c775b0bec62dac5444330ee982745e09322 volehong-sc <<EMAIL>> 1755501706 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
4e057c775b0bec62dac5444330ee982745e09322 90b403110dce6550de9f24b55af2167c073b3430 volehong-sc <<EMAIL>> 1755503377 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
90b403110dce6550de9f24b55af2167c073b3430 5af71d5d7e2d6329015da092ea2d989644c54ebe volehong-sc <<EMAIL>> 1755503428 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
5af71d5d7e2d6329015da092ea2d989644c54ebe 365c78aeb4b5510724e3eff436cb55116b161ea0 volehong-sc <<EMAIL>> 1755514284 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
365c78aeb4b5510724e3eff436cb55116b161ea0 358db44d047e75b2e574397d6baa1fcecb2f1b1c volehong-sc <<EMAIL>> 1755514338 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
358db44d047e75b2e574397d6baa1fcecb2f1b1c 6d01773a13566d0d88d86077497e2fa9e90a03af volehong-sc <<EMAIL>> 1755515351 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
6d01773a13566d0d88d86077497e2fa9e90a03af 76ed4312671a6bf93b233d6816af01eedf27c25e volehong-sc <<EMAIL>> 1755515747 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
76ed4312671a6bf93b233d6816af01eedf27c25e c49bd52a899ba54d470b04d42a6468757c4b08b8 volehong-sc <<EMAIL>> 1755515831 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
c49bd52a899ba54d470b04d42a6468757c4b08b8 a1988186b88b73866ff0acba2e5f7d8136c6817f volehong-sc <<EMAIL>> 1755517180 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
a1988186b88b73866ff0acba2e5f7d8136c6817f 971f54b55bde8f133491377b0b3f52ef447e9eea volehong-sc <<EMAIL>> 1755570729 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
971f54b55bde8f133491377b0b3f52ef447e9eea 65212364e8392a4b4f5f13bf882eb732efa40e9c volehong-sc <<EMAIL>> 1755571441 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
65212364e8392a4b4f5f13bf882eb732efa40e9c 6a795deefcf158213fa7dac77d2e789750a27545 volehong-sc <<EMAIL>> 1755572354 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
6a795deefcf158213fa7dac77d2e789750a27545 7f601aa2679b0be85f6f80d772c52ec247faa641 volehong-sc <<EMAIL>> 1755572689 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
7f601aa2679b0be85f6f80d772c52ec247faa641 420a9df1111041a0822deee84e6993dcfedacec9 volehong-sc <<EMAIL>> 1755577897 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
420a9df1111041a0822deee84e6993dcfedacec9 bacdc7d957042e779dd00e5ded968a848603aeef volehong-sc <<EMAIL>> 1755579733 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
bacdc7d957042e779dd00e5ded968a848603aeef 2e9d468abf325ac53ae60bdf9a744fa5777bc029 volehong-sc <<EMAIL>> 1755584330 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
2e9d468abf325ac53ae60bdf9a744fa5777bc029 a995a15fa358feb78e51702a16f5534fdfaeb2ec volehong-sc <<EMAIL>> 1755585782 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
a995a15fa358feb78e51702a16f5534fdfaeb2ec 80218f5d045d9716a8fc3379788208055afe9696 volehong-sc <<EMAIL>> 1755586144 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
80218f5d045d9716a8fc3379788208055afe9696 a4063c69cc1670549bbed55560a7e2eff46436b6 volehong-sc <<EMAIL>> 1755586440 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
a4063c69cc1670549bbed55560a7e2eff46436b6 40837c250e9a05dd3d20bbf0ce57c429222f8e89 volehong-sc <<EMAIL>> 1755586570 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
40837c250e9a05dd3d20bbf0ce57c429222f8e89 0c350359c8d1443026d730c16c575ec2ec0e0588 volehong-sc <<EMAIL>> 1755587367 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
0c350359c8d1443026d730c16c575ec2ec0e0588 075d4c3eca72351ed4474215bff107cac5c11ac7 volehong-sc <<EMAIL>> 1755587748 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
075d4c3eca72351ed4474215bff107cac5c11ac7 88d3e7a00678eb4e37c3f52b98e259ca2576bce4 volehong-sc <<EMAIL>> 1755588118 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
88d3e7a00678eb4e37c3f52b98e259ca2576bce4 564851fd4a023f615ab9c70ebd721fd8c4892cb7 volehong-sc <<EMAIL>> 1755588476 +0700	commit (amend): DCPF-48538: IssuerコントラクトをLogicコントラクトとStorageコントラクトに分割
564851fd4a023f615ab9c70ebd721fd8c4892cb7 34ebfedef617956c476de8ebb7d2824369fb0126 volehong-sc <<EMAIL>> 1755592457 +0700	commit: DCPF-48538: update test
