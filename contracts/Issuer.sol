// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/IIssuerStorage.sol";
import "./interfaces/IIssuer.sol";

// ライブラリのimport
import "./libraries/IssuerLogicCallLib.sol";
import "./libraries/IssuerLogicExecuteLib.sol";
import {IssuerData} from "./interfaces/Struct.sol";
import "./interfaces/Error.sol";

/**
 * @dev Issuerコントラクト
 */
contract Issuer is Initializable, IIssuer {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev IssuerStorageアドレス */
    IIssuerStorage private _issuerStorage;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param issuerStorage IssuerStorageアドレス
     */
    function initialize(IContractManager contractManager, IIssuerStorage issuerStorage)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && address(issuerStorage) != address(0),
            Error.RV0006_ISSUER_INVALID_VAL
        );
        _contractManager = contractManager;
        _issuerStorage = issuerStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // verify sender functions
    ///////////////////////////////////

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(
            bytes(err).length == 0 && has,
            bytes(err).length > 0 ? err : Error.GA0009_ISSUER_NOT_ADMIN_ROLE
        );
    }

    /**
     * @dev Issuer権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param issuerId issuerId
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _issuerOnly(
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
            IssuerLogicCallLib.getIssuerData(_issuerStorage, issuerId).role,
            hash,
            deadline,
            signature
        );
        require(
            bytes(err).length == 0 && has,
            bytes(err).length > 0 ? err : Error.GA0007_ISSUER_NOT_ROLE
        );
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Issuerの追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param bankCode 金融機関コード
     * @param name issuer名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addIssuer(
        bytes32 issuerId,
        uint16 bankCode,
        string memory name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(issuerId, bankCode, name, deadline)), deadline, signature);
        IssuerLogicCallLib.checkAddIssuerIsValid(_issuerStorage, bankCode);
        IssuerLogicExecuteLib.executeAddIssuer(
            _issuerStorage,
            _contractManager,
            issuerId,
            bankCode,
            name
        );
        emit AddIssuer(issuerId, bankCode, name, traceId);
    }

    /**
     * @dev issuerにaccountを紐付ける。
     *
     * ```
     * emit event: AddAccountByIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param traceId トレースID
     */
    function addAccountId(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        IssuerLogicCallLib.checkAddAccountIdIsValid(
            _contractManager,
            _issuerStorage,
            msg.sender,
            issuerId,
            accountId
        );
        IssuerLogicExecuteLib.executeAddAccountId(_issuerStorage, issuerId, accountId);
        emit AddAccountByIssuer(issuerId, accountId, traceId);
    }

    /**
     * @dev issuer権限の追加。
     *
     * ```
     * emit event: AddIssuerRole()
     * ```
     *
     * @param issuerId issuerId
     * @param issuerEoa issuerEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function addIssuerRole(
        bytes32 issuerId,
        address issuerEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(issuerId, issuerEoa, deadline)), deadline, signature);
        IssuerLogicCallLib.checkAddIssuerRoleIsValid(_issuerStorage, issuerId, issuerEoa);
        IssuerData memory issuerData = _issuerStorage.getIssuerData(issuerId);
        _contractManager.accessCtrl().addRoleByIssuer(issuerId, issuerData.role, issuerEoa);
        emit AddIssuerRole(issuerId, issuerEoa, traceId);
    }

    /**
     * @dev account権限の追加。
     *
     * ```
     * emit event: AddAccountRole()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param accountEoa accountEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addAccountRole(
        bytes32 issuerId,
        bytes32 accountId,
        address accountEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLogicCallLib.checkIssuerHasAccount(_issuerStorage, issuerId, accountId);
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, accountEoa, deadline)),
            deadline,
            signature
        );
        _contractManager.account().addAccountRole(accountId, accountEoa, traceId);
    }

    /**
     * @dev アカウントの状態を更新する。
     *
     * ```
     * emit event: AccountEnabled()
     * ```
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setAccountStatus(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLogicCallLib.checkIssuerHasAccount(_issuerStorage, issuerId, accountId);
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, reasonCode, deadline)),
            deadline,
            signature
        );
        _contractManager.account().setAccountStatus(accountId, accountStatus, reasonCode, traceId);
    }

    /**
     * @dev issuer名の更新。
     *
     * ```
     * emit event: ModIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param name issuer名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modIssuer(
        bytes32 issuerId,
        string memory name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(issuerId, name, deadline)), deadline, signature);
        IssuerLogicCallLib.checkHasIssuer(_issuerStorage, issuerId);
        IssuerLogicExecuteLib.updateIssuerName(_issuerStorage, issuerId, name);
        emit ModIssuer(issuerId, name, traceId);
    }

    /**
     * @dev Accountの限度額を更新する。
     *
     * ```
     * emit event: ModTokenLimit()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function modTokenLimit(
        bytes32 issuerId,
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLogicCallLib.checkIssuerHasAccountNotTerminated(
            _contractManager,
            _issuerStorage,
            issuerId,
            accountId
        );
        _issuerOnly(
            issuerId,
            abi.encode(issuerId, accountId, limitUpdates, limitValues, deadline),
            deadline,
            signature
        );

        limitValues = _contractManager.financialZoneAccount().modAccountLimit(
            accountId,
            limitUpdates,
            limitValues
        );
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);
        emit ModTokenLimit(validatorId, accountId, limitUpdates, limitValues, traceId);
    }

    /**
     * @dev Accountの累積限度額初期化。
     *
     * ```
     * emit event: CumulativeReset()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function cumulativeReset(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLogicCallLib.checkIssuerHasAccountNotTerminated(
            _contractManager,
            _issuerStorage,
            issuerId,
            accountId
        );
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, deadline)),
            deadline,
            signature
        );
        _contractManager.financialZoneAccount().cumulativeReset(accountId);
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);
        emit CumulativeReset(issuerId, accountId, traceId, validatorId);
    }

    /**
     * @dev アカウントを強制償却させる。AccountコントラクトのforceBurnを呼び出す。
     *
     * ```
     * emit event: ForceBurn()
     * ```
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function forceBurn(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLogicCallLib.checkIssuerHasAccount(_issuerStorage, issuerId, accountId);
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, deadline)),
            deadline,
            signature
        );
        _contractManager.account().forceBurn(accountId, traceId);
    }

    /**
     * @dev Fin Account 及び Biz Accountの部分強制償却を行う。
     *
     * ```
     * emit event: ForceBurn()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function partialForceBurn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLogicCallLib.checkIssuerHasAccount(_issuerStorage, issuerId, accountId);
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, burnedAmount, burnedBalance, deadline)),
            deadline,
            signature
        );
        _contractManager.account().partialForceBurn(
            accountId,
            burnedAmount,
            burnedBalance,
            traceId
        );
    }

    /**
     * @dev Biz ZoneとIssuerの紐付けを削除する。
     *
     * ```
     * emit event: DeleteBizZoneToIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function deleteBizZoneToIssuer(
        bytes32 issuerId,
        uint16 zoneId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(issuerId, zoneId, deadline)), deadline, signature);
        _contractManager.provider().deleteBizZoneToIssuer(issuerId, zoneId);
        emit DeleteBizZoneToIssuer(issuerId, zoneId, traceId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付けを追加する。
     *
     * ```
     * emit event: AddBizZoneToIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addBizZoneToIssuer(
        bytes32 issuerId,
        uint16 zoneId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        _adminOnly(keccak256(abi.encode(issuerId, zoneId, deadline)), deadline, signature);
        _contractManager.provider().addBizZoneToIssuer(issuerId, zoneId);
        emit AddBizZoneToIssuer(issuerId, zoneId, traceId);
    }

    /**
     * @dev 指定されたissuerIdに紐づくIssuer情報を登録、もしくは上書きする。
     *      バックアップリストア作業時のみ実行。
     *
     * @param issuer Issuer情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setIssuerAll(
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    ) external {
        IssuerLogicExecuteLib.setIssuerAll(_issuerStorage, issuer, deadline, signature);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev issuerの存在確認。
     *
     * @param issuerId issuerId
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasIssuer(bytes32 issuerId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return IssuerLogicCallLib.hasIssuer(_issuerStorage, issuerId);
    }

    /**
     * @dev issuerのリストを取得する。
     * TODO: DeccuretIssuerを含む前Issuerを返却する関数を別途作成する
     *
     * @param limit limit
     * @param offset offset
     * @return issuers issuers
     * @return totalCount issuerの数
     * @return err エラーメッセージ
     */
    function getIssuerList(uint256 limit, uint256 offset)
        external
        view
        override
        returns (
            IssuerListData[] memory issuers,
            uint256 totalCount,
            string memory err
        )
    {
        return IssuerLogicCallLib.getIssuerList(_issuerStorage, limit, offset);
    }

    /**
     * @dev 指定されたIssuerIDにAccountが紐付いているか確認を行う。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 issuerId, bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return IssuerLogicCallLib.hasAccount(_issuerStorage, issuerId, accountId);
    }

    /**
     * @dev issuerの情報を取得する。
     *
     * @param issuerId issuerId
     * @return name issuerの名前
     * @return bankCode 金融機関コード
     * @return err エラーメッセージ
     */
    function getIssuer(bytes32 issuerId)
        external
        view
        override
        returns (
            string memory name,
            uint16 bankCode,
            string memory err
        )
    {
        (bool exists, string memory error) = IssuerLogicCallLib.hasIssuer(_issuerStorage, issuerId);
        if (!exists) {
            return ("", 0, error);
        }
        IssuerData memory data = IssuerLogicCallLib.getIssuerData(_issuerStorage, issuerId);
        return (data.name, data.bankCode, "");
    }

    /**
     * @dev Issuerに紐づくAccountの情報を取得する。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @return accountName アカウント名
     * @return balance アカウントの残高
     * @return accountStatus アカウントの状態
     * @return reasonCode reasonCode
     * @return err エラーメッセージ
     */
    function getAccount(bytes32 issuerId, bytes32 accountId)
        external
        view
        override
        returns (
            string memory accountName,
            uint256 balance,
            bytes32 accountStatus,
            bytes32 reasonCode,
            string memory err
        )
    {
        return IssuerLogicCallLib.getAccount(_issuerStorage, _contractManager, issuerId, accountId);
    }

    /**
     * @dev 該当IssuerIDに紐づくAccountIDを取得する。
     *
     * @param issuerId issuerId
     * @param inAccountIds inAccountIds
     * @param limit limit
     * @param offset offset
     * @return accounts 指定のアカウントを取得する
     * @return totalCount アカウントの総数
     * @return err エラーメッセージ
     */
    function getAccountList(
        bytes32 issuerId,
        bytes32[] memory inAccountIds,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        return
            IssuerLogicCallLib.getAccountList(
                _issuerStorage,
                _contractManager,
                issuerId,
                inAccountIds,
                limit,
                offset
            );
    }

    /**
     * @dev issuerの数を返却する。
     *
     * @return count issuerの数
     */
    function getIssuerCount() external view override returns (uint256 count) {
        return IssuerLogicCallLib.getIssuerIdsCount(_issuerStorage);
    }

    /**
     * @dev indexに対応するissuerIdの取得。
     *
     * @param index index
     * @return issuerId issuerId
     * @return err エラーメッセージ
     */
    function getIssuerId(uint256 index)
        external
        view
        override
        returns (bytes32 issuerId, string memory err)
    {
        return
            index >= IssuerLogicCallLib.getIssuerIdsCount(_issuerStorage)
                ? (bytes32(0), Error.UE0110_VALIDATOR_OUT_OF_INDEX)
                : (IssuerLogicCallLib.getIssuerIdByIndex(_issuerStorage, index), "");
    }

    /**
     * @dev 権限チェック。
     *
     * @param issuerId issuerId
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return has true:権限あり,false:権限なし
     * @return err エラーメッセージ
     */
    function checkRole(
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        (bool validInput, string memory validationErr) = IssuerLogicCallLib.checkIssuerIdIsValid(
            issuerId
        );
        if (!validInput) return (false, validationErr);
        return
            _contractManager.accessCtrl().checkRole(
                IssuerLogicCallLib.getIssuerData(_issuerStorage, issuerId).role,
                hash,
                deadline,
                signature
            );
    }

    /**
     * @dev FinZoneコイン発行前確認
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Mintする数量
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return success true:チェックOK,false:チェックNG
     * @return err エラーメッセージ
     */
    function checkMint(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, amount, deadline)),
            deadline,
            signature
        );
        return
            IssuerLogicCallLib.checkMintIsValid(
                _contractManager,
                _issuerStorage,
                issuerId,
                accountId,
                amount,
                deadline,
                signature
            );
    }

    /**
     * @dev アカウントの凍結状態を確認する。
     *
     * @param accountId アカウントID
     * @return frozen true:凍結中,false:凍結中でない
     * @return err エラーメッセージ
     */
    function isFrozen(bytes32 accountId) external view returns (bool frozen, string memory err) {
        return IssuerLogicCallLib.isAccountFrozen(_contractManager, accountId);
    }

    /**
     * @dev FinZoneコイン償却前確認。
     *
     * @param issuerId issuerId
     * @param accountId アカウントID
     * @param amount 償却額
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return success true:チェックOK,false:チェックNG
     * @return err エラーメッセージ
     */
    function checkBurn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        _issuerOnly(
            issuerId,
            keccak256(abi.encode(issuerId, accountId, amount, deadline)),
            deadline,
            signature
        );
        return
            IssuerLogicCallLib.checkBurnIsValid(
                _contractManager,
                _issuerStorage,
                issuerId,
                accountId,
                amount,
                deadline,
                signature
            );
    }

    /**
     * @dev バックアップ用に発行者データを取得する。
     *
     * @param index index
     * @return issuer IssuerAll構造体
     */
    function getIssuerAll(uint256 index) external view returns (IssuerAll memory issuer) {
        return IssuerLogicCallLib.getIssuerAll(_issuerStorage, index);
    }
}
