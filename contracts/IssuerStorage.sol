// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IIssuerStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev IssuerStorageコントラクト
 *      Issuerデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract IssuerStorage is Initializable, IIssuerStorage {
    using RemigrationLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev IssuerLogicコントラクトアドレス */
    address private _issuerLogicAddr;

    /** @dev 検証者ID */
    bytes32[] private _issuerIds;

    /** @dev 発行者IDの存在確認フラグ(issuerId => boolean) */
    mapping(bytes32 => bool) private _issuerIdExistence;

    /** @dev 発行者IDに紐づくアカウントIdの存在確認フラグ(issuerId => accountId => boolean) */
    mapping(bytes32 => mapping(bytes32 => bool)) private _accountIdExistenceByIssuerId;

    /** @dev 発行者データ */
    mapping(bytes32 => IssuerData) private _issuerData;

    /** @dev setIssuersAllのsignature検証用 */
    string private constant SET_ISSUERS_ALL_SIGNATURE = "setIssuersAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev IssuerLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier issuerLogicOnly() {
        require(msg.sender == _issuerLogicAddr, Error.INVALID_CALLER_ADDRESS);
        _;
    }

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(modifier)。
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    modifier adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0009_ISSUER_NOT_ADMIN_ROLE);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     * @param contractManager ContractManagerアドレス
     * @param issuerLogicAddr IssuerLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address issuerLogicAddr)
        public
        initializer
    {
        require(address(contractManager) != address(0), Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerLogicAddr != address(0), Error.RV0006_ISSUER_INVALID_VAL);
        _contractManager = contractManager;
        _issuerLogicAddr = issuerLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev 発行者データを取得する
     * @param issuerId 発行者ID
     * @return issuerData 発行者データ
     */
    function getIssuerData(bytes32 issuerId)
        external
        view
        issuerLogicOnly
        returns (IssuerData memory issuerData)
    {
        return _issuerData[issuerId];
    }

    /**
     * @dev 発行者データを設定する
     * @param issuerId 発行者ID
     * @param issuerData 発行者データ
     */
    function setIssuerData(bytes32 issuerId, IssuerData memory issuerData)
        external
        issuerLogicOnly
    {
        _issuerData[issuerId] = issuerData;
    }

    /**
     * @dev 発行者データの名前を更新する
     * @param issuerId 発行者ID
     * @param name 新しい名前
     */
    function updateIssuerName(bytes32 issuerId, string memory name) external issuerLogicOnly {
        _issuerData[issuerId].name = name;
    }

    /**
     * @dev 発行者にアカウントIDを追加する
     * @param issuerId 発行者ID
     * @param accountId 追加するアカウントID
     */
    function addAccountIdToIssuer(bytes32 issuerId, bytes32 accountId) external issuerLogicOnly {
        _issuerData[issuerId].accountIds.push(accountId);
    }

    /**
     * @dev 発行者IDを配列に追加する
     * @param issuerId 追加する発行者ID
     */
    function addIssuerId(bytes32 issuerId) external issuerLogicOnly {
        require(issuerId != 0x00, Error.RV0006_ISSUER_INVALID_VAL);
        require(!_issuerIdExistence[issuerId], Error.GE1004_ISSUER_ID_EXIST);
        _issuerIds.push(issuerId);
    }

    /**
     * @dev 発行者IDの配列を取得する
     * @return issuerIds 発行者IDの配列
     */
    function getIssuerIds() external view issuerLogicOnly returns (bytes32[] memory) {
        return _issuerIds;
    }

    /**
     * @dev 発行者IDを指定インデックスで取得する
     * @param index インデックス
     * @return issuerId 発行者ID
     */
    function getIssuerIdByIndex(uint256 index) external view issuerLogicOnly returns (bytes32) {
        require(index < _issuerIds.length, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        return _issuerIds[index];
    }

    /**
     * @dev 発行者IDの総数を取得する
     * @return count 発行者IDの総数
     */
    function getIssuerIdsCount() external view issuerLogicOnly returns (uint256) {
        return _issuerIds.length;
    }

    /**
     * @dev 発行者IDの存在フラグを取得する
     * @param issuerId 発行者ID
     * @return exists 存在フラグ
     */
    function getIssuerIdExistence(bytes32 issuerId) external view issuerLogicOnly returns (bool) {
        return _issuerIdExistence[issuerId];
    }

    /**
     * @dev 発行者IDの存在フラグを設定する
     * @param issuerId 発行者ID
     * @param exists 存在フラグ
     */
    function setIssuerIdExistence(bytes32 issuerId, bool exists) external issuerLogicOnly {
        _issuerIdExistence[issuerId] = exists;
    }

    /**
     * @dev 発行者IDに紐づくアカウントIDの存在フラグを取得する
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistenceByIssuerId(bytes32 issuerId, bytes32 accountId)
        external
        view
        issuerLogicOnly
        returns (bool)
    {
        return _accountIdExistenceByIssuerId[issuerId][accountId];
    }

    /**
     * @dev 発行者IDに紐づくアカウントIDの存在フラグを設定する
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param exists 存在フラグ
     */
    function setAccountIdExistenceByIssuerId(
        bytes32 issuerId,
        bytes32 accountId,
        bool exists
    ) external issuerLogicOnly {
        _accountIdExistenceByIssuerId[issuerId][accountId] = exists;
    }

    /**
     * @dev バックアップ用に全発行者データを設定する（Admin権限必要）
     * @param issuer 全発行者データ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setIssuerAll(
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    )
        external
        override
        adminOnly(keccak256(abi.encode(SET_ISSUERS_ALL_SIGNATURE, deadline)), deadline, signature)
    {
        _issuerIds.push(issuer.issuerId);
        RemigrationLib.setIssuerAll(
            _issuerData[issuer.issuerId],
            _issuerIds,
            _issuerIdExistence,
            _accountIdExistenceByIssuerId,
            address(_contractManager),
            issuer
        );
    }

    /**
     * @dev バックアップ用に発行者データを取得する
     * @param index インデックス
     * @return issuer 全発行者データ
     */
    function getIssuerAll(uint256 index) external view override returns (IssuerAll memory issuer) {
        require(index < _issuerIds.length, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        bytes32 issuerId = _issuerIds[index];
        return
            RemigrationLib.getIssuerAll(
                _issuerData[issuerId],
                _issuerIds,
                _issuerIdExistence,
                _accountIdExistenceByIssuerId,
                address(_contractManager),
                index
            );
    }

    /**
     * @dev ContractManagerアドレスを更新する（将来の拡張用）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external {
        require(contractManagerAddr != address(0), Error.RV0006_ISSUER_INVALID_VAL);
        _contractManager = IContractManager(contractManagerAddr);
    }

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view override returns (address) {
        return address(_contractManager);
    }

    /**
     * @dev IssuerLogicアドレスを更新する（Admin権限必要）
     * @param issuerLogicAddr 新しいIssuerLogicアドレス
     */
    function setIssuerLogicAddress(address issuerLogicAddr) external {
        require(issuerLogicAddr != address(0), Error.RV0006_ISSUER_INVALID_VAL);
        _issuerLogicAddr = issuerLogicAddr;
    }
}
