// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IIssuerStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev IssuerLogicExecuteLibライブラリ
 *      Issuerの実行関数を実装するヘルパーライブラリ
 */
library IssuerLogicExecuteLib {
    /** @dev Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限)) */
    bytes32 public constant ROLE_PREFIX_ISSUER = keccak256("ISSUER_ROLE");

    /**
     * @dev Issuerの追加処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param contractManager ContractManager参照
     * @param issuerId issuerId
     * @param bankCode 金融機関コード
     * @param name issuer名
     */
    function executeAddIssuer(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        uint16 bankCode,
        string memory name
    ) external {
        issuerStorage.addIssuerId(issuerId);
        issuerStorage.setIssuerIdExistence(issuerId, true);
        bytes32 role = contractManager.accessCtrl().calcRole(ROLE_PREFIX_ISSUER, issuerId);
        IssuerData memory issuerData = IssuerData({
            role: role,
            name: name,
            bankCode: bankCode,
            accountIds: new bytes32[](0)
        });
        issuerStorage.setIssuerData(issuerId, issuerData);
    }

    /**
     * @dev issuerにaccountを紐付ける処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param issuerId issuerId
     * @param accountId accountId
     */
    function executeAddAccountId(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external {
        issuerStorage.addAccountIdToIssuer(issuerId, accountId);
        issuerStorage.setAccountIdExistenceByIssuerId(issuerId, accountId, true);
    }

    /**
     * @dev issuer権限の追加処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param contractManager ContractManager参照
     * @param issuerId issuerId
     * @param issuerEoa issuerEoa
     */
    function executeAddIssuerRole(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        address issuerEoa
    ) external {
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        contractManager.accessCtrl().addRoleByIssuer(issuerId, issuerData.role, issuerEoa);
    }

    /**
     * @dev issuer名の更新処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param issuerId issuerId
     * @param name issuer名
     */
    function updateIssuerName(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        string memory name
    ) external {
        issuerStorage.updateIssuerName(issuerId, name);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け削除処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param issuerId issuerId
     * @param zoneId zoneId
     */
    function deleteBizZoneToIssuer(
        IContractManager contractManager,
        bytes32 issuerId,
        uint16 zoneId
    ) external {
        contractManager.provider().deleteBizZoneToIssuer(issuerId, zoneId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け追加処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param issuerId issuerId
     * @param zoneId zoneId
     */
    function addBizZoneToIssuer(
        IContractManager contractManager,
        bytes32 issuerId,
        uint16 zoneId
    ) external {
        contractManager.provider().addBizZoneToIssuer(issuerId, zoneId);
    }

    /**
     * @dev 部分強制償却処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param contractManager ContractManager参照
     * @param issuerId issuerId
     * @param accountId accountId
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function partialForceBurn(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external {
        contractManager.account().partialForceBurn(accountId, burnedAmount, burnedBalance, traceId);
    }

    /**
     * @dev バックアップ用に全発行者データを設定する
     *
     * @param issuerStorage IssuerStorage参照
     * @param issuer 全発行者データ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setIssuerAll(
        IIssuerStorage issuerStorage,
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    ) external {
        issuerStorage.setIssuerAll(issuer, deadline, signature);
    }

    /**
     * @dev account権限の追加処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param issuerStorage IssuerStorage参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountEoa アカウントEOA
     * @param hash 権限チェック用ハッシュ
     * @param deadline 期限
     * @param signature 署名
     * @param traceId トレースID
     */
    function addAccountRole(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        address accountEoa,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature,
        bytes32 traceId
    ) external {
        contractManager.account().addAccountRole(accountId, accountEoa, traceId);
    }

    /**
     * @dev アカウントの状態を更新する処理を実行
     *
     * @param contractManager ContractManager参照
     * @param issuerStorage IssuerStorage参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     * @param hash 権限チェック用ハッシュ
     * @param deadline 期限
     * @param signature 署名
     * @param traceId トレースID
     */
    function setAccountStatus(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature,
        bytes32 traceId
    ) external {
        contractManager.account().setAccountStatus(accountId, accountStatus, reasonCode, traceId);
    }

    /**
     * @dev アカウントの限度額を更新する処理を実行
     *
     * @param contractManager ContractManager参照
     * @param issuerStorage IssuerStorage参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param limitUpdates 限度額更新フラグ
     * @param limitValues 限度額値
     * @param hash 権限チェック用ハッシュ
     * @param deadline 期限
     * @param signature 署名
     * @return updatedLimitValues 更新後の限度額値
     * @return validatorId アカウントに紐づくバリデータID
     */
    function executeModTokenLimit(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external returns (AccountLimitValues memory updatedLimitValues, bytes32 validatorId) {
        updatedLimitValues = contractManager.financialZoneAccount().modAccountLimit(
            accountId,
            limitUpdates,
            limitValues
        );
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);
        return (updatedLimitValues, validatorId);
    }

    /**
     * @dev アカウントの累積限度額初期化処理を実行
     *
     * @param contractManager ContractManager参照
     * @param issuerStorage IssuerStorage参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param hash 権限チェック用ハッシュ
     * @param deadline 期限
     * @param signature 署名
     * @return validatorId アカウントに紐づくバリデータID
     */
    function executeCumulativeReset(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external returns (bytes32 validatorId) {
        contractManager.financialZoneAccount().cumulativeReset(accountId);
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);
        return validatorId;
    }

    /**
     * @dev アカウントを強制償却する処理を実行
     *
     * @param contractManager ContractManager参照
     * @param issuerStorage IssuerStorage参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param hash 権限チェック用ハッシュ
     * @param deadline 期限
     * @param signature 署名
     * @param traceId トレースID
     */
    function forceBurn(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature,
        bytes32 traceId
    ) external {
        contractManager.account().forceBurn(accountId, traceId);
    }
}
