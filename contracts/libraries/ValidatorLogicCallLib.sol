// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IValidatorStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev ValidatorLogicCallLibライブラリ
 *      Validatorのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library ValidatorLogicCallLib {
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant MAX_LIMIT = 100;
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant MAX_LIMIT_1000 = 1000;
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_VALUE = 0x00;
    /** @dev 未登録の場合にて返す空の件数 */
    uint256 private constant EMPTY_LENGTH = 0;
    /** @dev 共通領域のID */
    uint16 private constant FINANCIAL_ZONE = 3000;
    /** @dev ソート制御用の固定値(降順) */
    bytes32 private constant DESC_SORT = keccak256(bytes("desc"));

    /** @dev バリデーション用のステータス値(申し込み) */
    bytes32 private constant STATUS_APPLYING = "applying";
    /** @dev バリデーション用のステータス値(解約申し込み) */
    bytes32 private constant STATUS_TERMINATING = "terminating";

    ///////////////////////////////////
    // Validation Functions
    ///////////////////////////////////

    /**
     * @dev validatorIdの入力検証
     */
    function checkValidatorIdIsValid(bytes32 validatorId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (validatorId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        return (true, "");
    }

    /**
     * @dev 検証者IDが登録済であるか確認する
     */
    function checkValidatorExists(bool exists, bytes32 validatorId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (validatorId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        success = exists;
        err = success ? "" : Error.GE0108_VALIDATOR_ID_NOT_EXIST;
        return (success, err);
    }

    /**
     * @dev 指定されたValidatorIDにAccountが紐付いているか確認を行う
     */
    function checkAccountExists(
        bool exists,
        bytes32 validatorId,
        bytes32 accountId
    ) external pure returns (bool success, string memory err) {
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        if (!exists) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev アカウント同期時のステータス検証
     */
    function checkSyncAccountStatusIsValid(bytes32 accountStatus, bool accountExistsInValidator)
        external
        pure
        returns (bool success, string memory err)
    {
        if (accountStatus == STATUS_APPLYING) {
            if (accountExistsInValidator) {
                return (false, Error.GE1010_ACCOUNT_ID_EXIST);
            }
        } else if (accountStatus == STATUS_TERMINATING) {
            if (!accountExistsInValidator) {
                return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
            }
        }
        return (true, "");
    }

    /**
     * @dev バリデータが直接管理するアカウントIDの存在確認
     */
    function checkValidatorAccountIdExists(bytes32 validatorAccountId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (validatorAccountId == EMPTY_VALUE) {
            return (false, Error.GE0109_VALIDATOR_ACCOUNT_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev Validator権限追加の検証処理
     */
    function checkAddValidatorRoleIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        address validatorEoa
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(validatorEoa != address(0), Error.RV0009_VALIDATOR_INVALID_VAL);
    }

    /**
     * @dev Validator名更新の検証処理
     */
    function checkModValidatorIsValid(IValidatorStorage validatorStorage, bytes32 validatorId)
        external
        view
    {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
    }

    /**
     * @dev Account名更新の検証処理
     */
    function checkModAccountIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view returns (bool success, string memory err) {
        bool exists = validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId);
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        if (!exists) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev Account追加の検証処理
     */
    function checkAddAccountIsValid(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(
            !validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId),
            Error.GE1010_ACCOUNT_ID_EXIST
        );
    }

    /**
     * @dev バリデータが直接管理するアカウントID追加の検証処理
     */
    function checkAddValidatorAccountIdIsValid(
        IContractManager contractManager,
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view {
        require(validatorId != bytes32(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(
            validatorStorage.getValidatorIdExistence(validatorId),
            Error.GE0108_VALIDATOR_ID_NOT_EXIST
        );
        require(accountId != bytes32(0), Error.RV0007_ACCOUNT_INVALID_VAL);
        (bool accountExists, ) = contractManager.account().hasAccount(accountId);
        require(accountExists, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        require(
            validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );
    }

    /**
     * @dev Zoneエラーがないことを確認
     */
    function checkZoneHasNoError(string memory zoneErr) external pure {
        require(bytes(zoneErr).length == 0, zoneErr);
    }

    ///////////////////////////////////
    // Storage Wrapper Functions
    ///////////////////////////////////

    /**
     * @dev バリデータIDの存在確認をストレージから取得
     */
    function getValidatorIdExistence(IValidatorStorage validatorStorage, bytes32 validatorId)
        external
        view
        returns (bool)
    {
        return validatorStorage.getValidatorIdExistence(validatorId);
    }

    /**
     * @dev バリデータIDに紐づくアカウントIDの存在確認をストレージから取得
     */
    function getAccountIdExistenceByValidatorId(
        IValidatorStorage validatorStorage,
        bytes32 validatorId,
        bytes32 accountId
    ) external view returns (bool) {
        return validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId);
    }

    /**
     * @dev バリデータデータをストレージから取得
     */
    function getValidatorData(IValidatorStorage validatorStorage, bytes32 validatorId)
        external
        view
        returns (ValidatorData memory)
    {
        return validatorStorage.getValidatorData(validatorId);
    }

    /**
     * @dev バリデータ数をストレージから取得
     */
    function getValidatorIdsCount(IValidatorStorage validatorStorage)
        external
        view
        returns (uint256)
    {
        return validatorStorage.getValidatorIdsCount();
    }

    /**
     * @dev インデックスによるバリデータIDをストレージから取得
     */
    function getValidatorIdByIndex(IValidatorStorage validatorStorage, uint256 index)
        external
        view
        returns (bytes32)
    {
        return validatorStorage.getValidatorIdByIndex(index);
    }

    /**
     * @dev バックアップ用の全バリデータデータを取得
     */
    function getValidatorAll(IValidatorStorage validatorStorage, uint256 index)
        external
        view
        returns (ValidatorAll memory)
    {
        return validatorStorage.getValidatorAll(index);
    }

    ///////////////////////////////////
    // Complex Query Functions
    ///////////////////////////////////

    /**
     * @dev 検証者情報リストを取得する
     */
    function getValidatorList(
        IValidatorStorage validatorStorage,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            ValidatorListData[] memory validators,
            uint256 totalCount,
            string memory err
        )
    {
        uint256 validatorCount = validatorStorage.getValidatorIdsCount();
        if (limit == 0 || validatorCount == 0) return (new ValidatorListData[](0), 0, "");
        if (limit > MAX_LIMIT)
            return (new ValidatorListData[](0), 0, Error.UE0111_VALIDATOR_TOO_LARGE_LIMIT);
        if (offset >= validatorCount)
            return (new ValidatorListData[](0), 0, Error.UE0112_VALIDATOR_OFFSET_OUT_OF_INDEX);

        uint256 size = (validatorCount >= offset + limit) ? limit : validatorCount - offset;
        validators = new ValidatorListData[](size);
        for (uint256 i = 0; i < size; i++) {
            bytes32 validatorId = validatorStorage.getValidatorIdByIndex(offset + i);
            ValidatorData memory data = validatorStorage.getValidatorData(validatorId);
            validators[i] = ValidatorListData({
                validatorId: validatorId,
                name: data.name,
                issuerId: data.issuerId
            });
        }
        return (validators, validatorCount, "");
    }

    /**
     * @dev 共通領域のアカウントに連携済みのzone情報を取得
     */
    function getZonesForFinancialAccount(
        IContractManager contractManager,
        bytes32 accountId,
        uint16 zoneId
    ) external view returns (ZoneData[] memory zones, string memory err) {
        if (zoneId != FINANCIAL_ZONE) {
            return (new ZoneData[](0), "");
        }
        zones = contractManager.account().getZoneByAccountId(accountId);
        return (zones, "");
    }

    /**
     * @dev Validatorに紐づくAccountの情報を取得する（限度額情報付き）
     */
    function getAccountWithLimits(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 accountId
    ) external view returns (AccountDataWithLimitData memory accountData, string memory err) {
        if (validatorId == bytes32(0)) {
            return (accountData, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        if (accountId == bytes32(0)) {
            return (accountData, Error.RV0009_VALIDATOR_INVALID_VAL);
        }
        if (!validatorStorage.getValidatorIdExistence(validatorId)) {
            return (accountData, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }
        if (!validatorStorage.getAccountIdExistenceByValidatorId(validatorId, accountId)) {
            return (accountData, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        AccountDataWithoutZoneId memory baseData;
        (baseData, err) = contractManager.account().getAccount(accountId);
        if (bytes(err).length > 0) return (accountData, err);

        accountData.accountName = baseData.accountName;
        accountData.accountStatus = baseData.accountStatus;
        accountData.balance = baseData.balance;
        accountData.reasonCode = baseData.reasonCode;
        accountData.appliedAt = baseData.appliedAt;
        accountData.registeredAt = baseData.registeredAt;
        accountData.terminatingAt = baseData.terminatingAt;
        accountData.terminatedAt = baseData.terminatedAt;

        (uint16 zoneId, , ) = contractManager.provider().getZone();
        if (zoneId == FINANCIAL_ZONE) {
            FinancialZoneAccountData memory limitData;
            (limitData, err) = contractManager.account().getAccountLimit(validatorId, accountId);
            if (bytes(err).length == 0) {
                accountData.mintLimit = limitData.mintLimit;
                accountData.burnLimit = limitData.burnLimit;
                accountData.transferLimit = limitData.transferLimit;
                accountData.chargeLimit = limitData.chargeLimit;
                accountData.dischargeLimit = limitData.dischargeLimit;
                accountData.cumulativeLimit = limitData.cumulativeLimit;
                accountData.cumulativeAmount = limitData.cumulativeAmount;
                accountData.cumulativeDate = limitData.cumulativeDate;
                accountData.cumulativeTransactionLimits = limitData.cumulativeTransactionLimits;
            }
        }
        return (accountData, "");
    }

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する
     */
    function getValidatorAccountList(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder
    )
        external
        view
        returns (
            ValidatorAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        if (!validatorStorage.getValidatorIdExistence(validatorId)) {
            return (new ValidatorAccountsData[](0), 0, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }

        bytes32[] memory accountIds = validatorStorage.getValidatorData(validatorId).accountIds;
        if (keccak256(bytes(sortOrder)) == DESC_SORT) {
            uint256 n = accountIds.length;
            for (uint256 i = 0; i < n / 2; i++) {
                bytes32 temp = accountIds[i];
                accountIds[i] = accountIds[n - i - 1];
                accountIds[n - i - 1] = temp;
            }
        }

        if (limit == 0 || accountIds.length == 0) {
            return (accounts, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (accounts, EMPTY_LENGTH, Error.UE0107_ACCOUNT_TOO_LARGE_LIMIT);
        }
        if (offset >= accountIds.length) {
            return (accounts, EMPTY_LENGTH, Error.UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX);
        }

        uint256 size = (accountIds.length >= offset + limit) ? limit : accountIds.length - offset;
        accounts = new ValidatorAccountsData[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = accountIds[offset + i];
            AccountDataWithoutZoneId memory accountData;
            accounts[i].accountId = accountId;
            (accountData, ) = contractManager.account().getAccount(accountId);
            accounts[i].accountName = accountData.accountName;
            accounts[i].balance = accountData.balance;
            accounts[i].accountStatus = accountData.accountStatus;
            accounts[i].reasonCode = accountData.reasonCode;
            accounts[i].appliedAt = accountData.appliedAt;
            accounts[i].registeredAt = accountData.registeredAt;
            accounts[i].terminatingAt = accountData.terminatingAt;
            accounts[i].terminatedAt = accountData.terminatedAt;
        }
        return (accounts, accountIds.length, "");
    }

    /**
     * @dev (CoreBatch専用 BCClient経由)該当ValidatorIDに紐づくAccountの情報を取得する
     */
    function getValidatorAccountAllList(
        IValidatorStorage validatorStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            ValidatorAccountsDataALL[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        if (!validatorStorage.getValidatorIdExistence(validatorId)) {
            return (new ValidatorAccountsDataALL[](0), 0, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }

        bytes32[] memory accountIds = validatorStorage.getValidatorData(validatorId).accountIds;
        uint256 n = accountIds.length;
        for (uint256 i = 0; i < n / 2; i++) {
            bytes32 temp = accountIds[i];
            accountIds[i] = accountIds[n - i - 1];
            accountIds[n - i - 1] = temp;
        }

        if (limit == 0 || accountIds.length == 0) {
            return (new ValidatorAccountsDataALL[](0), EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT_1000) {
            return (
                new ValidatorAccountsDataALL[](0),
                EMPTY_LENGTH,
                Error.UE0107_ACCOUNT_TOO_LARGE_LIMIT
            );
        }
        if (offset >= accountIds.length) {
            return (
                new ValidatorAccountsDataALL[](0),
                EMPTY_LENGTH,
                Error.UE0108_ACCOUNT_OFFSET_OUT_OF_INDEX
            );
        }

        uint256 size = (accountIds.length >= offset + limit) ? limit : accountIds.length - offset;
        accounts = new ValidatorAccountsDataALL[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = accountIds[offset + i];
            AccountDataAll memory accountData;
            accounts[i].accountId = accountId;
            (accountData, ) = contractManager.account().getAccountAll(accountId);
            accounts[i].accountDataAll = accountData;
        }
        return (accounts, accountIds.length, "");
    }
}
