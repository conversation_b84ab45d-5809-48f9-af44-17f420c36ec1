// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IIssuerStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";
import {IssuerStorage} from "../IssuerStorage.sol";

/**
 * @dev IssuerLogicCallLibライブラリ
 *      Issuerのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library IssuerLogicCallLib {
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_VALUE = 0x00;
    /** @dev 未登録の場合にて返す空の件数 */
    uint256 private constant EMPTY_LENGTH = 0;
    /** @dev 未登録の場合にて返す空のbytes32 */
    bytes32 private constant EMPTY_BYTES32 = 0x00;
    /** @dev 未登録の場合にて返す空のuint */
    uint16 private constant EMPTY_UINT = 0;
    /** @dev ディーカレット向けのbankCode */
    uint256 private constant DECCURET_BANK_CODE = 9999;

    /**
     * @dev issuerIdの入力検証
     *
     * @param issuerId チェック対象となる発行者ID
     * @return success true:有効,false:無効
     * @return err エラーメッセージ
     */
    function checkIssuerIdIsValid(bytes32 issuerId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        return (true, "");
    }

    /**
     * @dev 発行者情報リストを取得する。
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param limit limit
     * @param offset offset
     * @return issuers issuers
     * @return totalCount issuerの数
     * @return err エラーメッセージ
     */
    function getIssuerList(
        IIssuerStorage issuerStorage,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerListData[] memory issuers,
            uint256 totalCount,
            string memory err
        )
    {
        uint256 issuerIdsLength = issuerStorage.getIssuerIdsCount() > 0
            ? issuerStorage.getIssuerIdsCount() - 1
            : 0;

        if (limit == 0 || issuerIdsLength == 0) {
            return (issuers, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (issuers, EMPTY_LENGTH, Error.UE0102_ISSUER_TOO_LARGE_LIMIT);
        }
        if (offset >= issuerIdsLength) {
            return (issuers, EMPTY_LENGTH, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (issuerIdsLength >= offset + limit) ? limit : issuerIdsLength - offset;
        // issuer[0] を無効なissuerとみなしてスキップするための補正
        uint256 effectiveOffset = offset + 1;

        issuers = new IssuerListData[](size);
        for (uint256 i = 0; i < size; i++) {
            bytes32 issuerId = issuerStorage.getIssuerIdByIndex(effectiveOffset + i);
            IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
            issuers[i] = IssuerListData({
                issuerId: issuerId,
                name: issuerData.name,
                bankCode: issuerData.bankCode
            });
        }
        return (issuers, issuerIdsLength, "");
    }

    /**
     * @dev 該当IssuerIDに紐づくAccountIDを取得する。
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerId issuerId
     * @param inAccountIds inAccountIds
     * @param limit limit
     * @param offset offset
     * @return accounts 指定のアカウントを取得する
     * @return totalCount アカウントの総数
     * @return err エラーメッセージ
     */
    function getAccountList(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        bytes32[] memory inAccountIds,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        if (limit == 0 || inAccountIds.length == 0) {
            return (accounts, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (accounts, EMPTY_LENGTH, Error.UE0111_VALIDATOR_TOO_LARGE_LIMIT);
        }
        if (offset >= inAccountIds.length) {
            return (accounts, EMPTY_LENGTH, Error.UE0112_VALIDATOR_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (inAccountIds.length >= offset + limit)
            ? limit
            : inAccountIds.length - offset;

        accounts = new IssuerAccountsData[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = inAccountIds[offset + i];
            bool success;
            AccountDataWithoutZoneId memory accountData;

            success = issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId);
            if (success) {
                accounts[i].accountId = accountId;
                (accountData, ) = contractManager.account().getAccount(accountId);
                accounts[i].balance = accountData.balance;
                accounts[i].accountStatus = accountData.accountStatus;
                accounts[i].reasonCode = accountData.reasonCode;
            } else {
                accounts[i].accountId = 0;
                accounts[i].balance = 0;
                accounts[i].accountStatus = "";
                accounts[i].reasonCode = 0;
            }
        }

        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        return (accounts, issuerData.accountIds.length, "");
    }

    /**
     * @dev 発行者データを取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @return issuerData 発行者データ
     */
    function getIssuerData(IIssuerStorage issuerStorage, bytes32 issuerId)
        external
        view
        returns (IssuerData memory issuerData)
    {
        return issuerStorage.getIssuerData(issuerId);
    }

    /**
     * @dev 発行者IDの総数を取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @return count 発行者IDの総数
     */
    function getIssuerIdsCount(IIssuerStorage issuerStorage) external view returns (uint256) {
        return issuerStorage.getIssuerIdsCount();
    }

    /**
     * @dev 発行者IDを指定インデックスで取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param index インデックス
     * @return issuerId 発行者ID
     */
    function getIssuerIdByIndex(IIssuerStorage issuerStorage, uint256 index)
        external
        view
        returns (bytes32)
    {
        return issuerStorage.getIssuerIdByIndex(index);
    }

    /**
     * @dev バックアップ用に発行者データを取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param index インデックス
     * @return issuer 全発行者データ
     */
    function getIssuerAll(IIssuerStorage issuerStorage, uint256 index)
        external
        view
        returns (IssuerAll memory)
    {
        return issuerStorage.getIssuerAll(index);
    }

    /**
     * @dev Mint前の事前チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param amount Mint数量
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return success チェック結果
     * @return err エラーメッセージ
     */
    function checkMintIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        //Token存在確認
        (success, err) = contractManager.token().hasTokenState();
        if (!success) {
            return (success, err);
        }

        // ゾーンIDが取得できるかチェック
        (, , err) = contractManager.provider().getZone();
        if (bytes(err).length != 0) {
            return (false, err);
        }

        // 限度額チェック
        (success, err) = contractManager.financialZoneAccount().checkMint(accountId, amount);
        if (!success) {
            return (success, err);
        }

        return (success, "");
    }

    /**
     * @dev Burn前の事前チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param amount Burn数量
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return success チェック結果
     * @return err エラーメッセージ
     */
    function checkBurnIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        //Token存在確認
        (success, err) = contractManager.token().hasTokenState();
        if (!success) {
            return (success, err);
        }
        // 限度額チェック
        (, , err) = contractManager.provider().getZone();
        if (bytes(err).length != 0) {
            return (false, err);
        }
        // 限度額チェック
        (success, err) = contractManager.financialZoneAccount().checkBurn(accountId, amount);
        if (!success) {
            return (success, err);
        }
        // 残高チェック
        uint256 balance;
        (balance, err) = contractManager.account().balanceOf(accountId);
        if (balance < amount) {
            return (false, err);
        }

        return (success, "");
    }

    /**
     * @dev 発行者に紐づくアカウント情報を取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return accountName アカウント名
     * @return balance 残高
     * @return accountStatus アカウントステータス
     * @return reasonCode 理由コード
     * @return err エラーメッセージ
     */
    function getAccount(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        bytes32 accountId
    )
        external
        view
        returns (
            string memory accountName,
            uint256 balance,
            bytes32 accountStatus,
            bytes32 reasonCode,
            string memory err
        )
    {
        bool success;
        (success, err) = this.hasAccount(issuerStorage, issuerId, accountId);
        if (!success) {
            return ("", EMPTY_UINT, EMPTY_BYTES32, EMPTY_BYTES32, err);
        }

        (AccountDataWithoutZoneId memory accountData, string memory errTmp) = contractManager
            .account()
            .getAccount(accountId);

        if (bytes(errTmp).length != 0) {
            return ("", EMPTY_LENGTH, EMPTY_VALUE, EMPTY_VALUE, errTmp);
        }

        return (
            accountData.accountName,
            accountData.balance,
            accountData.accountStatus,
            accountData.reasonCode,
            ""
        );
    }

    /**
     * @dev アカウントの凍結状態を確認する
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId アカウントID
     * @return frozen 凍結状態
     * @return err エラーメッセージ
     */
    function isAccountFrozen(IContractManager contractManager, bytes32 accountId)
        external
        view
        returns (bool frozen, string memory err)
    {
        return contractManager.account().isFrozen(accountId);
    }

    /**
     * @dev IssuerRole追加の有効性チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param issuerEoa 発行者EOA
     */
    function checkAddIssuerRoleIsValid(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        address issuerEoa
    ) external view {
        (bool success, string memory err) = this.hasIssuer(issuerStorage, issuerId);
        require(success, err);
        require(issuerEoa != address(0), Error.RV0006_ISSUER_INVALID_VAL);
    }

    /**
     * @dev AddIssuer時のbankCode重複チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param bankCode 金融機関コード
     */
    function checkAddIssuerIsValid(IIssuerStorage issuerStorage, uint16 bankCode) external view {
        if (bankCode != DECCURET_BANK_CODE) {
            require(issuerStorage.getIssuerIdsCount() > 0, Error.GE2004_ISSUER_NOT_DECURRET);
        }
        uint256 issuerCount = issuerStorage.getIssuerIdsCount();
        for (uint256 i = 0; i < issuerCount; i++) {
            bytes32 existingIssuerId = issuerStorage.getIssuerIdByIndex(i);
            IssuerData memory existingIssuerData = issuerStorage.getIssuerData(existingIssuerId);
            require(existingIssuerData.bankCode != bankCode, Error.GE1009_ISSUER_EXIST_BANK_CODE);
        }
    }

    /**
     * @dev issuerの存在チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     */
    function checkHasIssuer(IIssuerStorage issuerStorage, bytes32 issuerId) external view {
        (bool success, string memory err) = this.hasIssuer(issuerStorage, issuerId);
        require(success, err);
    }

    /**
     * @dev issuerに紐づくaccountの存在チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function checkIssuerHasAccount(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view {
        (bool success, string memory err) = this.hasAccount(issuerStorage, issuerId, accountId);
        require(success, err);
    }

    /**
     * @dev 発行者の存在確認。
     *
     * @param issuerId チェック対象となる発行者Id
     * @param issuerIdExistence 発行者IDが登録済フラグ
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasIssuer(IIssuerStorage issuerStorage, bytes32 issuerId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (issuerId == 0x00) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        if (!issuerStorage.getIssuerIdExistence(issuerId)) {
            return (false, Error.GE0104_ISSUER_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev 指定されたIssuerIDにAccountが紐付いているか確認を行う(内部関数)。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasAccount(
        IssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) internal view returns (bool success, string memory err) {
        (success, err) = this.hasIssuer(issuerStorage, issuerId);
        require(success, err);

        if (accountId == 0x00) {
            return (false, Error.RV0017_INVALID_ACCOUNT_ID);
        }
        if (!issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId)) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev トークン限度額更新の検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function checkIssuerHasAccountNotTerminated(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view {
        this.checkIssuerHasAccount(issuerStorage, issuerId, accountId);
        this.checkAccountNotTerminated(contractManager, accountId);
    }

    /**
     * @dev アカウント終了チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId アカウントID
     */
    function checkAccountNotTerminated(IContractManager contractManager, bytes32 accountId)
        external
        view
    {
        (bool terminated, string memory terminatedErr) = contractManager.account().isTerminated(
            accountId
        );
        require(bytes(terminatedErr).length == 0, terminatedErr);
        require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);
    }

    /**
     * @dev addAccountIdの検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param sender メッセージ送信者
     * @param issuerId 発行者ID
     */
    function checkAddAccountIdIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        address sender,
        bytes32 issuerId,
        bytes32 accountId
    ) external view {
        require(
            sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        (bool success, string memory errTmp) = this.hasIssuer(issuerStorage, issuerId);
        require(success, errTmp);
        require(
            !issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE1010_ACCOUNT_ID_EXIST
        );
    }
}
