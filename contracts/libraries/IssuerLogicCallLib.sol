// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/IIssuerStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev IssuerLogicCallLibライブラリ
 *      Issuerのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library IssuerLogicCallLib {
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の値 */
    bytes32 private constant EMPTY_VALUE = 0x00;
    /** @dev 未登録の場合にて返す空の件数 */
    uint256 private constant EMPTY_LENGTH = 0;
    /** @dev 未登録の場合にて返す空のuint */
    uint16 private constant EMPTY_UINT = 0;
    /** @dev ディーカレット向けのbankCode */
    uint256 private constant DECCURET_BANK_CODE = 9999;

    /**
     * @dev issuerIdの入力検証
     *
     * @param issuerId チェック対象となる発行者ID
     * @return success true:有効,false:無効
     * @return err エラーメッセージ
     */
    function checkIssuerIdIsValid(bytes32 issuerId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        return (true, "");
    }

    /**
     * @dev 発行者IDが登録済であるか確認する。
     *
     * @param exists 存在フラグ
     * @param issuerId チェック対象となる発行者ID
     * @return success true:登録済,false:未登録
     * @return err エラーメッセージ
     */
    function checkIssuerExists(bool exists, bytes32 issuerId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        success = exists;
        err = success ? "" : Error.GE0104_ISSUER_ID_NOT_EXIST;
        return (success, err);
    }

    /**
     * @dev accountIdの入力検証
     *
     * @param accountId チェック対象となるアカウントID
     * @return success true:有効,false:無効
     * @return err エラーメッセージ
     */
    function checkAccountIdIsValid(bytes32 accountId)
        external
        pure
        returns (bool success, string memory err)
    {
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        return (true, "");
    }

    /**
     * @dev 指定されたIssuerIDにAccountが紐付いているか確認を行う。
     *
     * @param exists アカウントIDが発行者IDに紐付き済フラグ
     * @param issuerId issuerId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function checkAccountExists(
        bool exists,
        bytes32 issuerId,
        bytes32 accountId
    ) external pure returns (bool success, string memory err) {
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }

        success = exists;
        err = success ? "" : Error.GE0105_ACCOUNT_ID_NOT_EXIST;

        return (success, err);
    }

    /**
     * @dev 発行者情報リストを取得する。
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param limit limit
     * @param offset offset
     * @return issuers issuers
     * @return totalCount issuerの数
     * @return err エラーメッセージ
     */
    function getIssuerList(
        IIssuerStorage issuerStorage,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerListData[] memory issuers,
            uint256 totalCount,
            string memory err
        )
    {
        uint256 issuerIdsLength = issuerStorage.getIssuerIdsCount() > 0
            ? issuerStorage.getIssuerIdsCount() - 1
            : 0;

        if (limit == 0 || issuerIdsLength == 0) {
            return (issuers, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (issuers, EMPTY_LENGTH, Error.UE0102_ISSUER_TOO_LARGE_LIMIT);
        }
        if (offset >= issuerIdsLength) {
            return (issuers, EMPTY_LENGTH, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        }

        uint256 size = (issuerIdsLength >= offset + limit) ? limit : issuerIdsLength - offset;
        uint256 effectiveOffset = offset + 1;

        issuers = new IssuerListData[](size);
        for (uint256 i = 0; i < size; i++) {
            bytes32 issuerId = issuerStorage.getIssuerIdByIndex(effectiveOffset + i);
            IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
            issuers[i].issuerId = issuerId;
            issuers[i].name = issuerData.name;
            issuers[i].bankCode = issuerData.bankCode;
        }
        return (issuers, issuerIdsLength, "");
    }

    /**
     * @dev 該当IssuerIDに紐づくAccountIDを取得する。
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerId issuerId
     * @param inAccountIds inAccountIds
     * @param limit limit
     * @param offset offset
     * @return accounts 指定のアカウントを取得する
     * @return totalCount アカウントの総数
     * @return err エラーメッセージ
     */
    function getAccountList(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        bytes32[] memory inAccountIds,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        if (limit == 0 || inAccountIds.length == 0) {
            return (accounts, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (accounts, EMPTY_LENGTH, Error.UE0111_VALIDATOR_TOO_LARGE_LIMIT);
        }
        if (offset >= inAccountIds.length) {
            return (accounts, EMPTY_LENGTH, Error.UE0112_VALIDATOR_OFFSET_OUT_OF_INDEX);
        }

        uint256 size = (inAccountIds.length >= offset + limit)
            ? limit
            : inAccountIds.length - offset;

        accounts = new IssuerAccountsData[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = inAccountIds[offset + i];
            bool success;
            AccountDataWithoutZoneId memory accountData;

            success = issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId);
            if (success) {
                accounts[i].accountId = accountId;
                (accountData, ) = contractManager.account().getAccount(accountId);
                accounts[i].balance = accountData.balance;
                accounts[i].accountStatus = accountData.accountStatus;
                accounts[i].reasonCode = accountData.reasonCode;
            } else {
                accounts[i].accountId = 0;
                accounts[i].balance = 0;
                accounts[i].accountStatus = "";
                accounts[i].reasonCode = 0;
            }
        }

        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        return (accounts, issuerData.accountIds.length, "");
    }

    /**
     * @dev 発行者データを取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @return issuerData 発行者データ
     */
    function getIssuerData(IIssuerStorage issuerStorage, bytes32 issuerId)
        external
        view
        returns (IssuerData memory issuerData)
    {
        return issuerStorage.getIssuerData(issuerId);
    }

    /**
     * @dev 発行者IDの存在確認フラグを取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @return exists 存在フラグ
     */
    function getIssuerIdExistence(IIssuerStorage issuerStorage, bytes32 issuerId)
        external
        view
        returns (bool)
    {
        return issuerStorage.getIssuerIdExistence(issuerId);
    }

    /**
     * @dev 発行者IDの総数を取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @return count 発行者IDの総数
     */
    function getIssuerIdsCount(IIssuerStorage issuerStorage) external view returns (uint256) {
        return issuerStorage.getIssuerIdsCount();
    }

    /**
     * @dev 発行者IDを指定インデックスで取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param index インデックス
     * @return issuerId 発行者ID
     */
    function getIssuerIdByIndex(IIssuerStorage issuerStorage, uint256 index)
        external
        view
        returns (bytes32)
    {
        return issuerStorage.getIssuerIdByIndex(index);
    }

    /**
     * @dev 発行者IDに紐づくアカウントIDの存在確認フラグを取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistenceByIssuerId(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view returns (bool) {
        return issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId);
    }

    /**
     * @dev バックアップ用に発行者データを取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param index インデックス
     * @return issuer 全発行者データ
     */
    function getIssuerAll(IIssuerStorage issuerStorage, uint256 index)
        external
        view
        returns (IssuerAll memory)
    {
        return issuerStorage.getIssuerAll(index);
    }

    /**
     * @dev Issuer権限チェック用のvalidation
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return has 権限の有無
     * @return err エラーメッセージ
     */
    function checkIssuerRole(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err) {
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }

        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (has, err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
    }

    /**
     * @dev Account権限の追加可能性をチェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return success チェック結果
     * @return err エラーメッセージ
     */
    function checkAddAccountRoleIsValid(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view returns (bool success, string memory err) {
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }

        if (!issuerStorage.getIssuerIdExistence(issuerId)) {
            return (false, Error.GE0104_ISSUER_ID_NOT_EXIST);
        }

        if (!issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId)) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        return (true, "");
    }

    /**
     * @dev Mint前の事前チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param amount Mint数量
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return success チェック結果
     * @return err エラーメッセージ
     */
    function checkMintPreconditions(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        bytes32 hash = keccak256(abi.encode(issuerId, accountId, amount, deadline));
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }

        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (success, err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        if (bytes(err).length != 0 || !success) {
            return (false, bytes(err).length > 0 ? err : Error.GA0007_ISSUER_NOT_ROLE);
        }

        (success, err) = contractManager.token().hasTokenState();
        if (!success) {
            return (success, err);
        }

        (, , err) = contractManager.provider().getZone();
        if (bytes(err).length != 0) {
            return (false, err);
        }

        (success, err) = contractManager.financialZoneAccount().checkMint(accountId, amount);
        return (success, err);
    }

    /**
     * @dev Burn前の事前チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param amount Burn数量
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return success チェック結果
     * @return err エラーメッセージ
     */
    function checkBurnPreconditions(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        bytes32 hash = keccak256(abi.encode(issuerId, accountId, amount, deadline));
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }

        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (success, err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        if (bytes(err).length != 0 || !success) {
            return (false, bytes(err).length > 0 ? err : Error.GA0007_ISSUER_NOT_ROLE);
        }

        (success, err) = contractManager.token().hasTokenState();
        if (!success) {
            return (success, err);
        }

        (, , err) = contractManager.provider().getZone();
        if (bytes(err).length != 0) {
            return (false, err);
        }

        (success, err) = contractManager.financialZoneAccount().checkBurn(accountId, amount);
        if (!success) {
            return (success, err);
        }

        uint256 balance;
        (balance, err) = contractManager.account().balanceOf(accountId);
        if (balance < amount) {
            return (false, err);
        }

        return (true, "");
    }

    /**
     * @dev アカウント終了状態チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId アカウントID
     * @return terminated 終了状態
     * @return err エラーメッセージ
     */
    function checkAccountTerminated(IContractManager contractManager, bytes32 accountId)
        external
        view
        returns (bool terminated, string memory err)
    {
        return contractManager.account().isTerminated(accountId);
    }

    /**
     * @dev 発行者に紐づくアカウント情報を取得する
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return accountName アカウント名
     * @return balance 残高
     * @return accountStatus アカウントステータス
     * @return reasonCode 理由コード
     * @return err エラーメッセージ
     */
    function getIssuerAccount(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        bytes32 accountId
    )
        external
        view
        returns (
            string memory accountName,
            uint256 balance,
            bytes32 accountStatus,
            bytes32 reasonCode,
            string memory err
        )
    {
        if (!issuerStorage.getIssuerIdExistence(issuerId)) {
            return ("", EMPTY_LENGTH, EMPTY_VALUE, EMPTY_VALUE, Error.GE0104_ISSUER_ID_NOT_EXIST);
        }

        if (!issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId)) {
            return ("", EMPTY_LENGTH, EMPTY_VALUE, EMPTY_VALUE, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }

        (AccountDataWithoutZoneId memory accountData, string memory errTmp) = contractManager
            .account()
            .getAccount(accountId);

        if (bytes(errTmp).length != 0) {
            return ("", EMPTY_LENGTH, EMPTY_VALUE, EMPTY_VALUE, errTmp);
        }

        return (
            accountData.accountName,
            accountData.balance,
            accountData.accountStatus,
            accountData.reasonCode,
            ""
        );
    }

    /**
     * @dev 発行者IDを指定インデックスで取得する（エラーハンドリング付き）
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param index インデックス
     * @return issuerId 発行者ID
     * @return err エラーメッセージ
     */
    function getIssuerIdWithValidation(IIssuerStorage issuerStorage, uint256 index)
        external
        view
        returns (bytes32 issuerId, string memory err)
    {
        uint256 totalCount = issuerStorage.getIssuerIdsCount();
        if (index >= totalCount) {
            return (EMPTY_VALUE, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        }
        return (issuerStorage.getIssuerIdByIndex(index), "");
    }

    /**
     * @dev アカウントの凍結状態を確認する
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId アカウントID
     * @return frozen 凍結状態
     * @return err エラーメッセージ
     */
    function checkAccountFrozen(IContractManager contractManager, bytes32 accountId)
        external
        view
        returns (bool frozen, string memory err)
    {
        return contractManager.account().isFrozen(accountId);
    }

    /**
     * @dev Validatorコントラクトからの呼び出しかチェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param sender 呼び出し元アドレス
     */
    function checkValidatorContractOnly(IContractManager contractManager, address sender)
        external
        view
    {
        require(
            sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
    }

    /**
     * @dev IssuerIDの有効性チェック（addAccountId用）
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     */
    function checkAddAccountIdIsValid(IIssuerStorage issuerStorage, bytes32 issuerId)
        external
        view
    {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
    }

    /**
     * @dev IssuerRole追加の有効性チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param issuerEoa 発行者EOA
     */
    function checkAddIssuerRoleIsValid(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        address issuerEoa
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerEoa != address(0), Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
    }

    /**
     * @dev Issuer名更新の有効性チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     */
    function checkModIssuerIsValid(IIssuerStorage issuerStorage, bytes32 issuerId) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
    }

    /**
     * @dev IssuerとAccountの関連性をチェック（hasAccount用）
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return success チェック結果
     * @return err エラーメッセージ
     */
    function checkIssuerHasAccount(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view returns (bool success, string memory err) {
        if (!issuerStorage.getIssuerIdExistence(issuerId)) {
            return (false, Error.GE0104_ISSUER_ID_NOT_EXIST);
        }

        bool exists = issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId);
        if (issuerId == EMPTY_VALUE) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        if (accountId == EMPTY_VALUE) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }

        success = exists;
        err = success ? "" : Error.GE0105_ACCOUNT_ID_NOT_EXIST;

        return (success, err);
    }

    /**
     * @dev IssuerIDとAccountIDの存在チェックを統合
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function checkIssuerAndAccountExistForExecute(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );
    }

    /**
     * @dev Issuerの基本的なvalidation
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountEoa アカウントEOA
     */
    function checkBasicIssuerExecuteParams(
        bytes32 issuerId,
        bytes32 accountId,
        address accountEoa
    ) external pure {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        require(accountEoa != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
    }

    /**
     * @dev Account存在確認
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function checkAccountExistsForExecute(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view {
        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );
    }

    /**
     * @dev Issuer権限チェック
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param hash ハッシュ値
     * @param deadline 期限
     * @param signature 署名
     */
    function checkIssuerRoleForExecute(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view {
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev Account解約状態確認
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId アカウントID
     */
    function checkAccountNotTerminated(IContractManager contractManager, bytes32 accountId)
        external
        view
    {
        (bool terminated, string memory terminatedErr) = contractManager.account().isTerminated(
            accountId
        );
        require(bytes(terminatedErr).length == 0, terminatedErr);
        require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);
    }

    /**
     * @dev AddIssuer時のbankCode重複チェック
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param bankCode 金融機関コード
     */
    function checkAddIssuerIsValid(IIssuerStorage issuerStorage, uint16 bankCode) external view {
        if (bankCode != DECCURET_BANK_CODE) {
            require(issuerStorage.getIssuerIdsCount() > 0, Error.GE2004_ISSUER_NOT_DECURRET);
        }
        uint256 issuerCount = issuerStorage.getIssuerIdsCount();
        for (uint256 i = 0; i < issuerCount; i++) {
            bytes32 existingIssuerId = issuerStorage.getIssuerIdByIndex(i);
            IssuerData memory existingIssuerData = issuerStorage.getIssuerData(existingIssuerId);
            require(existingIssuerData.bankCode != bankCode, Error.GE1009_ISSUER_EXIST_BANK_CODE);
        }
    }

    /**
     * @dev Account重複チェック（executeAddAccountId用）
     *
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function checkAccountNotAlreadyLinked(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external view {
        require(
            !issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE1010_ACCOUNT_ID_EXIST
        );
    }

    /**
     * @dev Account Role追加の検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountEoa アカウントEOA
     * @param hash ハッシュ値
     * @param deadline 期限
     * @param signature 署名
     */
    function checkAddAccountRoleIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        address accountEoa,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        require(accountEoa != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);

        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );

        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev 部分強制償却の検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param deadline 期限
     * @param signature 署名
     */
    function checkPartialForceBurnIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        uint256 deadline,
        bytes memory signature
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );

        bytes32 hash = keccak256(
            abi.encode(issuerId, accountId, burnedAmount, burnedBalance, deadline)
        );
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev アカウント状態更新の検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param reasonCode 理由コード
     * @param deadline 期限
     * @param signature 署名
     */
    function checkSetAccountStatusIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 reasonCode,
        uint256 deadline,
        bytes memory signature
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);

        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );

        bytes32 hash = keccak256(abi.encode(issuerId, accountId, reasonCode, deadline));
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev トークン限度額更新の検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param limitUpdates 限度額更新フラグ
     * @param limitValues 限度額値
     * @param deadline 期限
     * @param signature 署名
     */
    function checkModTokenLimitIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues,
        uint256 deadline,
        bytes memory signature
    ) external view {
        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );

        (bool terminated, string memory terminatedErr) = contractManager.account().isTerminated(
            accountId
        );
        require(bytes(terminatedErr).length == 0, terminatedErr);
        require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);

        bytes32 hash = keccak256(
            abi.encode(issuerId, accountId, limitUpdates, limitValues, deadline)
        );
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev 累積限度額リセットの検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param deadline 期限
     * @param signature 署名
     */
    function checkCumulativeResetIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 deadline,
        bytes memory signature
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);

        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );

        (bool terminated, string memory terminatedErr) = contractManager.account().isTerminated(
            accountId
        );
        require(bytes(terminatedErr).length == 0, terminatedErr);
        require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);

        bytes32 hash = keccak256(abi.encode(issuerId, accountId, deadline));
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev 強制償却の検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param deadline 期限
     * @param signature 署名
     */
    function checkForceBurnIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId,
        uint256 deadline,
        bytes memory signature
    ) external view {
        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
        require(accountId != EMPTY_VALUE, Error.RV0007_ACCOUNT_INVALID_VAL);
        require(
            issuerStorage.getAccountIdExistenceByIssuerId(issuerId, accountId),
            Error.GE0105_ACCOUNT_ID_NOT_EXIST
        );

        bytes32 hash = keccak256(abi.encode(issuerId, accountId, deadline));
        IssuerData memory issuerData = issuerStorage.getIssuerData(issuerId);
        (bool has, string memory err) = contractManager.accessCtrl().checkRole(
            issuerData.role,
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    /**
     * @dev addAccountIdの検証処理（統合版）
     *
     * @param contractManager ContractManagerコントラクト参照
     * @param issuerStorage IssuerStorageコントラクト参照
     * @param sender メッセージ送信者
     * @param issuerId 発行者ID
     */
    function checkAddAccountIdIsValid(
        IContractManager contractManager,
        IIssuerStorage issuerStorage,
        address sender,
        bytes32 issuerId
    ) external view {
        require(
            sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );

        require(issuerId != EMPTY_VALUE, Error.RV0006_ISSUER_INVALID_VAL);
        require(issuerStorage.getIssuerIdExistence(issuerId), Error.GE0104_ISSUER_ID_NOT_EXIST);
    }
}
